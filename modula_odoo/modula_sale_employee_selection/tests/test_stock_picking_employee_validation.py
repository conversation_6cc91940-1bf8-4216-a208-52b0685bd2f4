# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError
from unittest.mock import patch, MagicMock


class TestStockPickingEmployeeValidation(TransactionCase):
    """Test stock picking employee validation with backorder wizard handling"""

    def setUp(self):
        super().setUp()
        
        # Create test employee
        self.employee = self.env['hr.employee'].create({
            'name': 'Test Employee',
            'pin': '1234',
            'employee_type': 'employee'
        })
        
        # Create test product
        self.product = self.env['product.product'].create({
            'name': 'Test Product',
            'type': 'product',
        })
        
        # Create test stock picking
        self.picking = self.env['stock.picking'].create({
            'picking_type_id': self.env.ref('stock.picking_type_out').id,
            'location_id': self.env.ref('stock.stock_location_stock').id,
            'location_dest_id': self.env.ref('stock.stock_location_customers').id,
        })
        
        # Create stock move
        self.move = self.env['stock.move'].create({
            'name': 'Test Move',
            'product_id': self.product.id,
            'product_uom_qty': 10,
            'product_uom': self.product.uom_id.id,
            'picking_id': self.picking.id,
            'location_id': self.env.ref('stock.stock_location_stock').id,
            'location_dest_id': self.env.ref('stock.stock_location_customers').id,
        })

    def test_employee_validate_picking_success(self):
        """Test successful picking validation without wizard"""
        
        # Mock button_validate to return True (successful validation)
        with patch.object(self.picking.__class__, 'button_validate', return_value=True):
            
            # Set context for stock picking
            employee = self.employee.with_context(
                res_model='stock.picking',
                res_id=self.picking.id
            )
            
            result = employee.employee_validate_picking()
            
            # Should return success status for form refresh
            self.assertIsInstance(result, dict)
            self.assertTrue(result.get('success'))
            self.assertTrue(result.get('refresh_form'))
            self.assertEqual(result.get('message'), 'Picking validated successfully')

    def test_employee_validate_picking_with_wizard(self):
        """Test picking validation that returns backorder wizard"""
        
        # Mock wizard action that would be returned by button_validate
        wizard_action = {
            'type': 'ir.actions.act_window',
            'name': 'Create Backorder?',
            'res_model': 'stock.backorder.confirmation',
            'view_mode': 'form',
            'target': 'new',
        }
        
        # Mock button_validate to return wizard action
        with patch.object(self.picking.__class__, 'button_validate', return_value=wizard_action):
            
            # Set context for stock picking
            employee = self.employee.with_context(
                res_model='stock.picking',
                res_id=self.picking.id
            )
            
            result = employee.employee_validate_picking()
            
            # Should return wizard action for frontend execution
            self.assertIsInstance(result, dict)
            self.assertTrue(result.get('success'))
            self.assertEqual(result.get('wizard_action'), wizard_action)
            self.assertEqual(result.get('message'), 'Validation requires wizard confirmation')

    def test_employee_validate_picking_error(self):
        """Test picking validation with error"""
        
        # Mock button_validate to raise exception
        with patch.object(self.picking.__class__, 'button_validate', side_effect=Exception("Validation failed")):
            
            # Set context for stock picking
            employee = self.employee.with_context(
                res_model='stock.picking',
                res_id=self.picking.id
            )
            
            result = employee.employee_validate_picking()
            
            # Should return error status
            self.assertIsInstance(result, dict)
            self.assertFalse(result.get('success'))
            self.assertIn('Validation failed', result.get('error', ''))

    def test_login_with_stock_picking_context(self):
        """Test employee login with stock picking context returns validation result"""
        
        # Mock successful validation
        validation_result = {
            'success': True,
            'refresh_form': True,
            'message': 'Picking validated successfully'
        }
        
        with patch.object(self.employee.__class__, 'employee_validate_picking', return_value=validation_result):
            
            # Set context for stock picking
            employee = self.employee.with_context(
                res_model='stock.picking',
                res_id=self.picking.id
            )
            
            result = employee.login(pin='1234')
            
            # Should return the validation result
            self.assertEqual(result, validation_result)

    def test_stock_picking_button_validate_requires_employee(self):
        """Test that button_validate requires employee selection"""
        
        # Mock request session without session_owner
        with patch('odoo.http.request') as mock_request:
            mock_request.session = {}
            
            # Should raise ValidationError
            with self.assertRaises(ValidationError) as cm:
                self.picking.button_validate()
            
            self.assertIn('Please select an employee', str(cm.exception))

    def test_stock_picking_button_validate_with_employee(self):
        """Test button_validate with employee in session"""
        
        # Mock request session with session_owner
        with patch('odoo.http.request') as mock_request:
            mock_request.session = {'session_owner': self.employee.id}
            
            # Mock super().button_validate() to return True
            with patch('odoo.addons.stock.models.stock_picking.StockPicking.button_validate', return_value=True):
                
                result = self.picking.button_validate()
                
                # Should assign employee and clear session
                self.assertEqual(self.picking.employee_id.id, self.employee.id)
                self.assertFalse(mock_request.session.get('session_owner'))
                self.assertTrue(result)

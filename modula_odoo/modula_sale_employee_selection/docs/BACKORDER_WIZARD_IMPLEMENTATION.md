# Backorder Wizard Implementation - Employee Selection Module

## 🎯 **Overview**

This document describes the implementation of backorder wizard handling in the employee selection module for stock picking validation. The solution ensures that when `button_validate()` returns a backorder wizard action, the wizard is properly executed instead of being lost due to form refresh.

## 🚨 **Problem Statement**

### **Original Issue**
When validating stock pickings through employee selection:
1. Employee selection popup → PIN validation → `employee_validate_picking()` 
2. `button_validate()` may return a backorder wizard action
3. Frontend immediately refreshes form → Wizard action is lost
4. User never sees the backorder confirmation wizard

### **Required Solution**
- Detect when `button_validate()` returns a wizard action
- Execute the wizard action in frontend instead of refreshing form
- Only refresh form after wizard completes or when no wizard is needed

## 🔧 **Implementation Details**

### **1. Backend Changes (Python)**

#### **Modified `hr_employee.py` - `employee_validate_picking()` method**

```python
def employee_validate_picking(self):
    """Validate picking after employee selection and handle backorder wizard if needed
    
    This method validates the picking and detects if a backorder wizard action is returned.
    If a wizard action is returned, it passes the action to the frontend for execution.
    If validation completes without wizard, it returns success status for form refresh.
    
    Returns:
        dict: Action dictionary if backorder wizard needed, or success status
    """
    picking = self.env[self.env.context.get('res_model')].browse(self.env.context.get('res_id'))
    
    try:
        # Call button_validate which may return a wizard action or True
        result = picking.with_context(skip_sms=True).button_validate()
        
        # Check if result is a wizard action (backorder confirmation)
        if isinstance(result, dict) and result.get('type') == 'ir.actions.act_window':
            # This is a wizard action (like backorder confirmation)
            # Return the action so frontend can execute it
            return {
                'wizard_action': result,
                'success': True,
                'message': 'Validation requires wizard confirmation'
            }
        else:
            # Validation completed successfully without wizard
            return {
                'success': True,
                'message': 'Picking validated successfully',
                'refresh_form': True
            }
            
    except Exception as e:
        # Handle validation errors
        return {
            'success': False,
            'error': str(e),
            'message': f'Validation failed: {str(e)}'
        }
```

#### **Modified `hr_employee.py` - `login()` method**

```python
def login(self, pin=False, set_in_session=True):
    if self.pin_validation(pin) and set_in_session:
        if self.env.context.get('action_approve_sale_order'):
            request.session[MANAGER_APPROVE] = self.id
        else:
            self._connect_employee()
            request.session[SESSION_OWNER] = self.id
            if self.env.context.get('res_model') == 'stock.picking' and self.env.context.get('res_id'):
                # Handle stock picking validation with potential wizard actions
                validation_result = self.employee_validate_picking()
                if isinstance(validation_result, dict):
                    # Return the validation result (may contain wizard action)
                    return validation_result
                else:
                    # Legacy return format
                    return validation_result
        return True
    return False
```

### **2. Frontend Changes (JavaScript)**

#### **Modified `employee_hooks.js` - `selectEmployee()` method**

```javascript
const selectEmployee = async (employeeId, pin) => {
    // ... existing code ...
    
    const loginResult = await orm.call("hr.employee", employee_function, [employeeId, pin], { context });
    
    // Handle different response formats
    let pinValid = false;
    let wizardAction = null;
    let shouldRefreshForm = false;
    
    if (typeof loginResult === 'boolean') {
        // Legacy format - simple boolean response
        pinValid = loginResult;
        shouldRefreshForm = pinValid && context.res_model === 'stock.picking';
    } else if (typeof loginResult === 'object' && loginResult !== null) {
        // New format - object with success, wizard_action, etc.
        pinValid = loginResult.success || false;
        wizardAction = loginResult.wizard_action || null;
        shouldRefreshForm = loginResult.refresh_form || false;
        
        if (!pinValid && loginResult.error) {
            console.error("Employee login error:", loginResult.error);
            notification.add(loginResult.message || _t("Login failed"), { type: "danger" });
            return;
        }
    }
    
    // ... validation logic ...
    
    // Handle stock picking validation results
    if (context.res_model === 'stock.picking') {
        if (wizardAction) {
            // Execute wizard action (backorder confirmation)
            console.log("Executing wizard action for stock picking:", wizardAction);
            try {
                // Store wizard action for execution by the form controller
                if (formSaveCallbacks && formSaveCallbacks.executeWizardAction) {
                    await formSaveCallbacks.executeWizardAction(wizardAction);
                    console.log("Wizard action executed successfully");
                } else {
                    console.warn("No executeWizardAction callback available");
                    notification.add(_t("Wizard action needs to be handled manually"), { type: "warning" });
                }
            } catch (error) {
                console.error("Error executing wizard action:", error);
                notification.add(_t("Error executing validation wizard"), { type: "danger" });
            }
        } else if (shouldRefreshForm) {
            // Direct validation without wizard - refresh form
            console.log("Stock picking validated directly - refreshing form");
            if (formSaveCallbacks && formSaveCallbacks.refreshForm) {
                try {
                    await formSaveCallbacks.refreshForm();
                    console.log("Stock picking form refreshed after direct validation");
                } catch (error) {
                    console.error("Error refreshing stock picking form:", error);
                    notification.add(_t("Error refreshing form"), { type: "danger" });
                }
            }
        }
    }
    
    // ... rest of method ...
};
```

#### **Modified `employee_selection_button.js` - Added `executeWizardAction()` method**

```javascript
async executeWizardAction(wizardAction) {
    try {
        console.log("Executing wizard action:", wizardAction);
        
        // Execute the wizard action using the action service
        const result = await this.env.services.action.doAction(wizardAction);
        console.log("Wizard action executed successfully:", result);
        
        // After wizard completes, refresh the form to show updated state
        setTimeout(async () => {
            try {
                await this.refreshStockPickingForm();
                console.log("Form refreshed after wizard completion");
            } catch (error) {
                console.error("Error refreshing form after wizard:", error);
            }
        }, 1000); // Delay to ensure wizard action completes
        
        return result;
    } catch (error) {
        console.error("Error executing wizard action:", error);
        this.env.services.notification.add("Error executing wizard", { type: "danger" });
        throw error;
    }
},
```

#### **Modified callback setup in `onPopEmployeeSelection()`**

```javascript
if (record.resModel === 'stock.picking') {
    this.useEmployee.setFormSaveCallbacks({
        refreshForm: this.refreshStockPickingForm.bind(this),
        executeWizardAction: this.executeWizardAction.bind(this),  // 🆕 NEW
        getRecordId: () => record.resId,
        resModel: record.resModel
    });
}
```

## 🔄 **New Workflow**

### **Updated Stock Picking Employee Selection Workflow**

```
1. User opens stock picking → "Validate" button appears (if no employee assigned)

2. User clicks "Validate" → onPopEmployeeSelection() triggered
   ├── Sets up form callbacks: refreshStockPickingForm() + executeWizardAction()
   ├── Context created: { res_model: 'stock.picking', res_id: picking_id }
   └── popupAddEmployee() opens selection popup

3. User selects employee → selectEmployee() called
   ├── Context includes res_model='stock.picking' and res_id
   ├── Employee validation and PIN check with context
   ├── login() method detects stock.picking context
   └── 🆕 CRITICAL: employee_validate_picking() called automatically

4. 🆕 Enhanced validation sequence:
   ├── employee_validate_picking() → picking.button_validate()
   ├── button_validate() checks session_owner exists
   ├── Assigns employee_id = session_owner
   ├── Clears session_owner = False
   ├── Calls super().button_validate() → May return wizard action or True
   └── 🆕 NEW: Detects wizard action vs direct completion

5. 🆕 Conditional response handling:
   ├── If wizard action returned:
   │   ├── Return { wizard_action: action, success: true }
   │   ├── Frontend receives wizard action
   │   ├── executeWizardAction() called
   │   ├── action.doAction(wizardAction) executed
   │   └── Form refreshed after wizard completion
   └── If direct validation:
       ├── Return { success: true, refresh_form: true }
       ├── Frontend receives success status
       └── refreshStockPickingForm() called immediately

6. ✅ Result: Employee selected, picking validated (with or without wizard), AND form refreshed appropriately
```

## 🧪 **Testing**

### **Test Cases Added**

1. **`test_employee_validate_picking_success()`**
   - Tests successful validation without wizard
   - Verifies return format: `{ success: true, refresh_form: true }`

2. **`test_employee_validate_picking_with_wizard()`**
   - Tests validation that returns backorder wizard
   - Verifies return format: `{ wizard_action: action, success: true }`

3. **`test_employee_validate_picking_error()`**
   - Tests validation with error
   - Verifies return format: `{ success: false, error: message }`

4. **`test_login_with_stock_picking_context()`**
   - Tests login method returns validation result
   - Verifies integration between login and employee_validate_picking

## 🎯 **Benefits**

### **User Experience**
- ✅ **Backorder wizard properly displayed** when needed
- ✅ **No lost wizard actions** due to premature form refresh
- ✅ **Seamless validation flow** with or without wizard
- ✅ **Proper error handling** for validation failures

### **Technical Benefits**
- ✅ **Backward compatibility** with existing validation flows
- ✅ **Clean separation** between wizard and non-wizard paths
- ✅ **Robust error handling** with detailed logging
- ✅ **Extensible pattern** for future wizard integrations

## 🚨 **Important Notes**

### **Response Format Detection**
The frontend handles both legacy boolean responses and new object responses:
- **Legacy**: `true/false` (backward compatibility)
- **New**: `{ success: boolean, wizard_action?: object, refresh_form?: boolean }`

### **Timing Considerations**
- **Wizard execution**: Uses `action.doAction()` for proper wizard handling
- **Form refresh delay**: 1-second timeout ensures wizard completes before refresh
- **Error recovery**: Graceful fallback if wizard execution fails

### **Context Preservation**
- All context (res_model, res_id) is preserved throughout the workflow
- Session management remains unchanged
- Employee assignment still occurs before validation

---

**Implementation Status**: ✅ **COMPLETE**  
**Tested**: ✅ **Unit tests added**  
**Backward Compatible**: ✅ **Legacy flows preserved**
